#!/bin/bash

# PostgreSQL Connection Monitor Script
# Usage: ./monitor_postgres_connections.sh

LOG_FILE="/var/log/postgresql/postgresql-16-main.log"
TEMP_LOG="/tmp/postgres_monitor.log"

echo "=== PostgreSQL Connection Monitor ==="
echo "Timestamp: $(date)"
echo "======================================="

# Check if PostgreSQL is running
echo "1. PostgreSQL Service Status:"
sudo systemctl is-active postgresql && echo "✅ PostgreSQL is running" || echo "❌ PostgreSQL is not running"
echo ""

# Check listening ports
echo "2. PostgreSQL Listening Ports:"
sudo netstat -tulpn | grep :5432 || echo "❌ PostgreSQL not listening on port 5432"
echo ""

# Check current connections
echo "3. Current Active Connections:"
sudo -u postgres psql -c "
SELECT 
    COUNT(*) as total_connections,
    COUNT(*) FILTER (WHERE state = 'active') as active_connections,
    COUNT(*) FILTER (WHERE state = 'idle') as idle_connections
FROM pg_stat_activity;" 2>/dev/null || echo "❌ Cannot connect to PostgreSQL"
echo ""

# Show recent connections from logs
echo "4. Recent Connection Attempts (last 50 lines):"
if [ -f "$LOG_FILE" ]; then
    sudo tail -50 "$LOG_FILE" | grep -i "connection\|authentication\|failed\|error" | tail -10
else
    echo "❌ Log file not found: $LOG_FILE"
fi
echo ""

# Test Docker container connection
echo "5. Testing Docker Container Connection:"
if command -v docker &> /dev/null; then
    docker run --rm postgres:16 psql -h 172.17.0.1 -U postgres -d tempfly_app -c "SELECT 'Connection successful' as status;" 2>/dev/null && echo "✅ Docker can connect to PostgreSQL" || echo "❌ Docker cannot connect to PostgreSQL"
else
    echo "⚠️  Docker not available for testing"
fi
echo ""

# Show connection details
echo "6. Detailed Connection Information:"
sudo -u postgres psql -c "
SELECT 
    client_addr,
    client_port,
    usename,
    datname,
    application_name,
    state,
    backend_start,
    query_start
FROM pg_stat_activity 
WHERE client_addr IS NOT NULL
ORDER BY backend_start DESC 
LIMIT 10;" 2>/dev/null || echo "❌ Cannot retrieve connection details"

echo ""
echo "======================================="
echo "Monitor completed at: $(date)"
